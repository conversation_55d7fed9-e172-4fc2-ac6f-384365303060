# Hệ Thống Nhận Dạng Ngôn Ngữ Ký Hiệu (Sign Language Recognition System)

## Giới Thiệu

Đây là một ứng dụng nhận dạng ngôn ngữ ký hiệu sử dụng trí tuệ nhân tạo (AI) và computer vision. Hệ thống có thể nhận dạng các ký hiệu tay và chuyển đổi thành chữ cái tiếng <PERSON>, gi<PERSON>p người khiếm thính giao tiếp dễ dàng hơn.

### Tính Năng Chính

- **Nhận dạng thời gian thực**: Sử dụng camera để nhận dạng ký hiệu tay trực tiếp
- **Đ<PERSON> chính xác cao**: Sử dụng mô hình CNN (Convolutional Neural Network) được huấn luyện trên dataset Sign Language MNIST
- **X<PERSON><PERSON> dựng từ thông minh**: <PERSON><PERSON> thống học hỏi từ các lỗi sửa chữa của người dùng
- **<PERSON><PERSON> nhớ ngữ cảnh**: <PERSON><PERSON> nhớ các mẫu xóa để cải thiện dự đoán
- **Giao diện trực quan**: Hiển thị tiến trình và thông tin chi tiết trên màn hình

### <PERSON>ác Chữ <PERSON>ái Được Hỗ Trợ

Hệ thống nhận dạng 24 chữ cái: A, B, <PERSON>, D, <PERSON>, <PERSON>, G, H, <PERSON>, K, L, M, N, O, P, Q, R, S, T, U, V, W, X, Y
(Không bao gồm J và Z do hạn chế của dataset)

## Yêu Cầu Hệ Thống

### Phần Cứng
- **Camera**: Webcam hoặc camera tích hợp
- **RAM**: Tối thiểu 4GB (khuyến nghị 8GB)
- **CPU**: Intel i5 hoặc tương đương
- **GPU**: Không bắt buộc nhưng khuyến nghị có GPU để tăng tốc độ xử lý

### Phần Mềm
- **Hệ điều hành**: Windows 10/11, macOS 10.14+, hoặc Ubuntu 18.04+
- **Python**: Phiên bản 3.7 - 3.10
- **Pip**: Để cài đặt các thư viện Python

## Cài Đặt

### Bước 1: Chuẩn Bị Môi Trường

```bash
# Tạo môi trường ảo Python (khuyến nghị)
python -m venv sign_language_env

# Kích hoạt môi trường ảo
# Trên Windows:
sign_language_env\Scripts\activate
# Trên macOS/Linux:
source sign_language_env/bin/activate
```

### Bước 2: Cài Đặt Thư Viện

```bash
# Cài đặt tất cả thư viện cần thiết
pip install -r requirements.txt
```

### Bước 3: Tải Dataset (Nếu Muốn Huấn Luyện Lại)

1. Tải dataset Sign Language MNIST từ [Kaggle](https://www.kaggle.com/datamunge/sign-language-mnist)
2. Đặt các file `sign_mnist_train.csv` và `sign_mnist_test.csv` vào thư mục gốc

## Sử Dụng

### Chạy Ứng Dụng Nhận Dạng

```bash
python predict.py
```

### Điều Khiển Ứng Dụng

Khi ứng dụng đang chạy, bạn có thể sử dụng các phím sau:

- **Giữ ký hiệu 3 giây**: Thêm chữ cái vào từ
- **Phím 's'**: Thêm khoảng trắng
- **Phím 'Backspace'**: Xóa chữ cái cuối và học từ lỗi
- **Phím 'n'**: Hoàn thành từ hiện tại và bắt đầu từ mới
- **Phím 'Enter'**: Lưu từ đã hoàn thành vào file
- **Phím 'q'**: Thoát ứng dụng

### Huấn Luyện Mô Hình Mới (Tùy Chọn)

```bash
python train.py
```

## Cách Thức Hoạt Động

### 1. Nhận Dạng Ký Hiệu
- Camera ghi lại hình ảnh tay người dùng
- MediaPipe phát hiện và theo dõi các điểm mốc trên tay
- Hệ thống tạo bounding box xung quanh bàn tay
- Hình ảnh được xử lý và chuẩn hóa về kích thước 28x28 pixels

### 2. Dự Đoán Chữ Cái
- Mô hình CNN phân tích hình ảnh đã xử lý
- Đưa ra dự đoán với độ tin cậy cho từng chữ cái
- Hệ thống lọc bỏ các chữ cái đã bị chặn

### 3. Xây Dựng Từ Thông Minh
- Yêu cầu giữ ký hiệu ổn định trong 3 giây để xác nhận
- Học hỏi từ các lần xóa của người dùng
- Sử dụng bộ nhớ ngữ cảnh để cải thiện dự đoán

## Cấu Trúc File

```
├── predict.py              # File chính để chạy ứng dụng nhận dạng
├── train.py               # File huấn luyện mô hình
├── requirements.txt       # Danh sách thư viện cần thiết
├── sign_mnist_train.csv   # Dataset huấn luyện (cần tải về)
├── sign_mnist_test.csv    # Dataset kiểm tra (cần tải về)
├── sign_language_model.h5 # Mô hình đã huấn luyện (được tạo sau khi train)
├── created_words.txt      # File lưu các từ đã tạo
├── training_history.png   # Biểu đồ quá trình huấn luyện
└── confusion_matrix.png   # Ma trận nhầm lẫn
```

## Xử Lý Sự Cố

### Lỗi Camera Không Hoạt Động
```
Error: Could not open camera.
```
**Giải pháp**: 
- Kiểm tra camera có được kết nối đúng không
- Đảm bảo không có ứng dụng nào khác đang sử dụng camera
- Thử thay đổi chỉ số camera trong code (từ 0 thành 1 hoặc 2)

### Lỗi Thiếu Mô Hình
```
FileNotFoundError: sign_language_model.h5
```
**Giải pháp**: 
- Chạy `python train.py` để huấn luyện mô hình mới
- Hoặc tải mô hình đã huấn luyện sẵn (nếu có)

### Lỗi Cài Đặt Thư Viện
```
ERROR: Could not install packages
```
**Giải pháp**: 
- Cập nhật pip: `pip install --upgrade pip`
- Cài đặt từng thư viện riêng lẻ
- Sử dụng conda thay vì pip nếu gặp xung đột

## Tối Ưu Hóa Hiệu Suất

### Để Có Độ Chính Xác Tốt Nhất:
1. **Ánh sáng**: Đảm bảo ánh sáng đủ và đều
2. **Nền**: Sử dụng nền đơn giản, tránh nền phức tạp
3. **Khoảng cách**: Giữ tay ở khoảng cách 30-50cm từ camera
4. **Ký hiệu**: Thực hiện ký hiệu rõ ràng và chính xác
5. **Ổn định**: Giữ tay ổn định trong 3 giây để hệ thống nhận dạng

### Cải Thiện Tốc Độ:
- Sử dụng GPU nếu có
- Giảm độ phân giải camera nếu cần
- Đóng các ứng dụng không cần thiết

## Đóng Góp và Phát Triển

Nếu bạn muốn đóng góp cho dự án:
1. Fork repository
2. Tạo branch mới cho tính năng
3. Commit các thay đổi
4. Tạo Pull Request

## Giấy Phép

Dự án này được phát hành dưới giấy phép MIT. Xem file LICENSE để biết thêm chi tiết.

## Liên Hệ và Hỗ Trợ

Nếu gặp vấn đề hoặc có câu hỏi, vui lòng:
- Tạo issue trên GitHub
- Kiểm tra phần xử lý sự cố ở trên
- Đọc kỹ hướng dẫn sử dụng

---

**Lưu ý**: Đây là dự án giáo dục và nghiên cứu. Để sử dụng trong môi trường sản xuất, cần thêm các tính năng bảo mật và tối ưu hóa hiệu suất.
